import json
import os
import functions_framework
from datetime import datetime, timezone
import traceback
import base64
import sqlalchemy
from google.cloud.sql.connector import Connector

from db import get_whatsapp_entries, close_db_connections
from report_generator import WhatsAppReportGenerator
from utils import upload_to_gcs, gcs_manager, sendMail

@functions_framework.cloud_event
def generate_report(cloud_event):
    """HTTP Cloud Function that generates WhatsApp reports
    Args:
        request (flask.Request): The request object
    Returns:
        The response text, or any set of values that can be turned into a
        Response object using make_response
    """
    pool = None
    user_email = None
    file_name = None
    public_url = None
    try:
        pubsub_message = base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
        message_data = json.loads(pubsub_message)

        if not message_data:
            return (json.dumps({'error': 'Invalid request format'}), 400)

        folder_name = message_data.get('folder_name')
        start_date_str = message_data.get('start_date')
        end_date_str = message_data.get('end_date')
        file_name = message_data.get('file_name')
        campaign_id = message_data.get('campaign_id')
        template_id = message_data.get('template_id')
        template_name = message_data.get('template_name')
        user_email = message_data.get('user_email')

        if not folder_name:
            return (json.dumps({'error': 'folder_name is required'}), 400)

        # Parse dates
        start_date = None
        end_date = None

        if start_date_str:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').replace(
                tzinfo=timezone.utc)
        if end_date_str:
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').replace(
                tzinfo=timezone.utc)
            # Set to end of day
            end_date = end_date.replace(hour=23, minute=59, second=59)

        connector = Connector()
        # function to return the database connection
        def get_db_connection():
            # db_host = os.environ.get('DB_HOST')
            instance_conn_name = os.environ['INSTANCE_CONNECTION_NAME']
            db_name = os.environ.get('DB_NAME')
            db_user = os.environ.get('DB_USER')
            db_password = os.environ.get('DB_PASSWORD')
            try:
                db_conn=connector.connect(
                    instance_conn_name,
                    'pg8000',
                    user=db_user,
                    password=db_password,
                    db=db_name
                )
                return db_conn
            except Exception as e:
                raise

        pool = sqlalchemy.create_engine("postgresql+pg8000://", creator=get_db_connection)

        # Generate the report
        report_generator = WhatsAppReportGenerator(pool)
        output_file = report_generator.generate_combined_report(
            campaign_id, template_id, template_name, folder_name, start_date, end_date, gcs_manager)

        # Upload the report to GCS
        output_path = f"notices/{campaign_id}/reports/{template_id}_{file_name}"
        # rewrite_file_path = f"notices/{folder_name}.xlsx"

        # Upload to GCS
        public_url = upload_to_gcs(gcs_manager, output_path, output_file)
        # public_url_rewrite = upload_to_gcs(gcs_manager, rewrite_file_path, output_file)

        # Return success response with the file URL
        # sendMail(user_email, f"WhatsApp delivery report generated - {file_name}", 
        #         f"You can download your WhatsApp delivery report using this link: {public_url}")
        return (json.dumps({
            'success': True,
            'message': 'Report generated successfully',
            'file_path': output_path,
            'file_url': public_url
        }), 200)
        return "success"

    except Exception as e:
        return (json.dumps({
            'error': f'Failed to generate report: {str(e)}'
        }), 500)

    finally:
        sendMail(user_email, f"WhatsApp delivery report generated - {file_name}", 
                f"You can download your WhatsApp delivery report using this link: {public_url}")
        close_db_connections(pool)  # Clean up connection pool

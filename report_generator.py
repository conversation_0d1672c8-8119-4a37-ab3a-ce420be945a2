
import io
import pandas as pd
from datetime import timedelta,datetime
from collections import defaultdict

from db import get_whatsapp_entries

class WhatsAppReportGenerator:
    def __init__(self, db_conn):
        self.db_conn = db_conn
        self.status_map = {
            'initiated': 'Message was initiated by the system',
            'sent': 'Message was sent from WhatsApp Business API',
            'delivered': 'Message was delivered to recipient\'s phone',
            'read': 'Recipient has read the message',
            'failed': 'Message failed to deliver'
        }

    def _debug_phone_numbers(self, data, label):
        """Helper function to debug phone number formats"""
        # Handle different data types properly
        if data is None:
            return
            
        if isinstance(data, list):
            if not data:  # Empty list
                return
            phones = [item.get('phone_number') if isinstance(item, dict) else item for item in data]
        elif hasattr(data, 'empty'):  # pandas Series
            if data.empty:
                return
            phones = data.tolist()
        elif hasattr(data, 'tolist'):  # Other array-like objects
            phones = data.tolist()
            if not phones:
                return
        else:
            # Single value
            phones = [str(data)]
            
        # Filter out None, empty, and 'None' string values
        phones = [p for p in phones if p is not None and str(p).strip() not in ['', 'None', 'nan', 'NaN']]
        if phones:
            # Count different formats
            with_91 = len([p for p in phones if str(p).startswith('91') and len(str(p)) == 12])
            ten_digit = len([p for p in phones if len(str(p)) == 10 and str(p).isdigit()])
            scientific = len([p for p in phones if 'e+' in str(p).lower() or 'E+' in str(p)])
            other = len(phones) - with_91 - ten_digit - scientific
            # print(f"Debug {label}: {len(phones)} phones - {with_91} with 91 prefix, {ten_digit} 10-digit, {scientific} scientific, {other} other")
        else:
            print(f"  - No valid phone numbers found")

    def _test_phone_normalization(self):
        """Test phone normalization with sample data from CSV"""
        test_cases = [
            "8341168437",        # Normal 10-digit
            "9849229423",        # Normal 10-digit  
            "9.19E+11",          # Scientific notation
            "9.20E+11",          # Scientific notation
            "9.16E+11",          # Scientific notation
            "9.18E+11",          # Scientific notation
            "919123456789",      # 12-digit with 91 prefix
            "0",                 # Zero/missing
            "",                  # Empty string
            None,                # None value
        ]
        for test_phone in test_cases:
            normalized = self.normalize_phone_number(test_phone)
            # print(f"Test: {test_phone} -> {normalized}")

    def normalize_phone_number(self, phone_number, status=None):
        """
        Normalize phone numbers across different statuses
        - Handle scientific notation (9.19E+11 format)
        - Remove '91' prefix if it exists (12 digits to 10 digits)
        - Clean any non-digit characters
        - Return standardized 10-digit number
        """
        if not phone_number or phone_number in [0, '0', 'nan', 'NaN']:
            return None
            
        phone_str = str(phone_number).strip().lower()
        
        # Handle scientific notation (e.g., 9.19e+11)
        if 'e+' in phone_str:
            try:
                # Convert scientific notation to integer
                phone_float = float(phone_number)
                # Convert to integer to avoid decimal places
                phone_int = int(phone_float)
                clean_phone = str(phone_int)
            except (ValueError, OverflowError):
                # If conversion fails, fall back to string cleaning
                clean_phone = ''.join(filter(str.isdigit, phone_str))
        else:
            # Regular phone number processing
            clean_phone = ''.join(filter(str.isdigit, phone_str))
        
        # Remove '91' prefix if it exists and results in 10-digit number
        if clean_phone.startswith('91') and len(clean_phone) == 12:
            clean_phone = clean_phone[2:]
        
        # Ensure we have a valid 10-digit number
        if len(clean_phone) == 10 and clean_phone.isdigit():
            # Debug logging for significant changes
            # if phone_str != clean_phone:
                # print(f"Phone normalized: {phone_str} -> {clean_phone}")
            return clean_phone
        
        # If not a valid 10-digit number, return None
        return None

    def generate_report(self, start_date=None, end_date=None):
        """
        Generate a comprehensive delivery report for WhatsApp messages
        Args:
            start_date: datetime object for report start date
            end_date: datetime object for report end date
        """
        # Get entries from the database
        entries = get_whatsapp_entries(self.db_conn, start_date, end_date)
        if entries:
            # for i, entry in enumerate(entries[:3]):  # Show first 3 entries
            #     print(f"Entry {i}: {entry}")
            
            # Debug phone numbers from database
            self._debug_phone_numbers([entry.get('phone_number') for entry in entries], "Database entries")
            
            # Check specifically for initiated entries
            initiated_entries = [entry for entry in entries if entry.get('status') == 'initiated']
            if initiated_entries:
                # for i, entry in enumerate(initiated_entries[:5]):
                    # print(f"Initiated entry {i}: {entry}")
                    
                # Show phone numbers after normalization
                initiated_phones = [self.normalize_phone_number(entry.get('phone_number'), 'initiated') for entry in initiated_entries[:5]]
                # print(f"Normalized initiated phones: {initiated_phones}")
        else:
            return {'total_messages': 0, 'status_breakdown': [], 'delivery_rate': 0, 'average_delivery_time': 'N/A', 'failed_messages': 0}, []

        # Group entries by status
        status_groups = defaultdict(list)
        for entry in entries:
            status_groups[entry['status']].append(entry)

        # Get all unique message IDs regardless of status
        all_msg_ids = set()
        for status, entries_list in status_groups.items():
            for entry in entries_list:
                all_msg_ids.add(entry['msg_id'])

        # Create a mapping of message IDs to status data
        msg_status_map = {}
        for status, entries_list in status_groups.items():
            for entry in entries_list:
                msg_id = entry['msg_id']
                if msg_id not in msg_status_map:
                    msg_status_map[msg_id] = {}
                
                msg_status_map[msg_id][status] = entry

        # Generate summary statistics
        total_messages = len(all_msg_ids)  # Use all unique message IDs instead of just initiated
        
        summary = {
            'total_messages': total_messages,
            'status_breakdown': [
                {'status': status, 'count': len(entries)} 
                for status, entries in status_groups.items()
            ],
            'delivery_rate': self._calculate_delivery_rate(status_groups, all_msg_ids),
            'average_delivery_time': self._calculate_avg_delivery_time(status_groups),
            'failed_messages': len(status_groups.get('failed', [])),
        }

        # Generate detailed message history with all timestamps
        detailed_data = []
        
        # Include all messages, not just those with 'initiated' status
        for index, msg_id in enumerate(all_msg_ids, 1):
            # Find the most informative status entry for this message to get basic info
            statuses_priority = ['initiated', 'sent', 'delivered', 'read', 'failed']
            primary_entry = None
            
            for status in statuses_priority:
                if status in msg_status_map.get(msg_id, {}):
                    primary_entry = msg_status_map[msg_id][status]
                    break
            
            if not primary_entry:
                # This should never happen as we built the map from statuses
                continue
                
            # Get phone number from the primary entry and normalize it
            phone_number = primary_entry.get('phone_number', '')
            # Always normalize phone numbers regardless of status
            phone_number = self.normalize_phone_number(phone_number, primary_entry.get('status'))
            
            # Initialize data for this message
            message_data = {
                'S.No': index,
                'msg_id': msg_id,
                'Phone Number': phone_number,
                'Initiated timestamp': None,
                'Sent timestamp': None,
                'Delivered timestamp': None,
                'Read timestamp': None,
                'Failed timestamp': None,
                'Failed Reason': None
            }

            # Add timestamps for each status that exists
            for status in ['initiated', 'sent', 'delivered', 'read', 'failed']:
                if status in msg_status_map.get(msg_id, {}):
                    entry = msg_status_map[msg_id][status]
                    message_data[f'{status.capitalize()} timestamp'] = entry['timestamp']
                    if status == 'failed':
                        message_data['Failed Reason'] = entry.get('failed_reason')
            
            detailed_data.append(message_data)
            
        if detailed_data:
            sample_phones = [data.get('Phone Number') for data in detailed_data[:5]]
            # print(f"Sample phones from detailed data: {sample_phones}")
            
            # Debug normalized phone numbers
            all_phones = [data.get('Phone Number') for data in detailed_data]
            self._debug_phone_numbers(all_phones, "Normalized phone numbers in report")
            
        return summary, detailed_data

    def export_to_excel(self, start_date=None, end_date=None):
        """Export report to Excel file"""
        summary, detailed_data = self.generate_report(start_date, end_date)

        # Create Excel writer object
        output = io.BytesIO()
        writer = pd.ExcelWriter(output, engine='xlsxwriter')

        # Convert detailed data to DataFrame
        df_detailed = pd.DataFrame(detailed_data)

        # Define columns for the detailed report
        detailed_columns = [
            'S.No', 'Phone Number', 'Initiated timestamp', 'Sent timestamp', 
            'Delivered timestamp', 'Read timestamp', 'Failed timestamp', 'Failed Reason'
        ]

        # Create empty DataFrame with necessary columns if no data
        if not detailed_data:
            df_detailed = pd.DataFrame(columns=detailed_columns)
        else:
            # Ensure all columns exist (even if empty)
            for col in detailed_columns:
                if col not in df_detailed.columns:
                    df_detailed[col] = None

            # Reorder columns to match requested format
            df_detailed = df_detailed[detailed_columns]

        # Format timestamp columns
        timestamp_columns = [col for col in df_detailed.columns if 'timestamp' in col]
        for col in timestamp_columns:
            df_detailed[col] = df_detailed[col].apply(
                lambda x: x.strftime('%Y-%m-%d %H:%M:%S') if pd.notnull(x) else ''
            )

        # Create summary DataFrame
        summary_data = {
            'Metric': [
                'Total Messages',
                'Delivery Rate',
                'Failed Messages',
                'Average Delivery Time'
            ],
            'Value': [
                summary['total_messages'],
                f"{summary['delivery_rate']}%",
                summary['failed_messages'],
                summary['average_delivery_time']
            ]
        }
        df_summary = pd.DataFrame(summary_data)

        # Status breakdown summary
        status_data = [{
            'Status': status['status'],
            'Description': self.status_map.get(status['status'], 'Unknown status'),
            'Count': status['count']
        } for status in summary['status_breakdown']]
        df_status = pd.DataFrame(status_data) if status_data else pd.DataFrame(columns=['Status', 'Description', 'Count'])

        # Write each DataFrame to a different worksheet
        df_summary.to_excel(writer, sheet_name='Summary', index=False)
        df_status.to_excel(writer, sheet_name='Status Breakdown', index=False)
        df_detailed.to_excel(writer, sheet_name='Message History', index=False)

        # Add formatting to the Excel file
        self._format_excel_workbook(writer, df_summary, df_status, df_detailed)

        # Close the writer and get the output
        writer.close()
        output.seek(0)
        return output

    def _calculate_delivery_rate(self, status_groups, all_msg_ids=None):
        """Calculate the delivery rate as a percentage"""
        # If no messages are found, return 0
        if all_msg_ids is None:
            initiated_count = len(status_groups.get('initiated', []))
            if initiated_count == 0:
                return 0
                
            # Original logic for backward compatibility
            # Get all message IDs that were initiated
            initiated_msg_ids = {entry['msg_id'] for entry in status_groups.get('initiated', [])}
            
            # Get all message IDs that were delivered or read
            delivered_msg_ids = {entry['msg_id'] for entry in status_groups.get('delivered', [])}
            read_msg_ids = {entry['msg_id'] for entry in status_groups.get('read', [])}
            
            # Count unique message IDs that have either delivered or read status
            delivered_count = len(delivered_msg_ids.union(read_msg_ids))
            
            return round((delivered_count / initiated_count) * 100, 2)
        else:
            # New logic using all message IDs
            total_count = len(all_msg_ids)
            if total_count == 0:
                return 0
                
            # Get all message IDs that were delivered or read
            delivered_msg_ids = {entry['msg_id'] for entry in status_groups.get('delivered', [])}
            read_msg_ids = {entry['msg_id'] for entry in status_groups.get('read', [])}
            
            # Count unique message IDs that have either delivered or read status
            delivered_count = len(delivered_msg_ids.union(read_msg_ids))
            
            return round((delivered_count / total_count) * 100, 2)

    def _calculate_avg_delivery_time(self, status_groups):
        """Calculate average time between sent and delivered status"""
        # Create dictionaries for quick lookup of sent and delivered entries by msg_id
        sent_entries = {entry['msg_id']: entry for entry in status_groups.get('sent', [])}

        # Combine delivered and read entries (take the earliest)
        delivered_entries = {}
        for entry in status_groups.get('delivered', []) + status_groups.get('read', []):
            msg_id = entry['msg_id']
            if msg_id not in delivered_entries or entry['timestamp'] < delivered_entries[msg_id]['timestamp']:
                delivered_entries[msg_id] = entry

        # Find message IDs that have both sent and delivered/read status
        valid_msg_ids = set(sent_entries.keys()).intersection(set(delivered_entries.keys()))

        if not valid_msg_ids:
            return "N/A"

        total_time = timedelta()
        count = 0

        for msg_id in valid_msg_ids:
            # Get sent and delivered timestamps for this message
            sent_entry = sent_entries[msg_id]
            delivered_entry = delivered_entries[msg_id]

            delivery_time = delivered_entry['timestamp'] - sent_entry['timestamp']
            if delivery_time.total_seconds() >= 0:  # Ensure valid time difference
                total_time += delivery_time
                count += 1

        if count == 0:
            return "N/A"

        avg_time = total_time / count
        # Format the timedelta in a more readable format (hours:minutes:seconds)
        total_seconds = avg_time.total_seconds()
        hours, remainder = divmod(total_seconds, 3600)
        minutes, seconds = divmod(remainder, 60)

        if hours > 0:
            return f"{int(hours)}h {int(minutes)}m {int(seconds)}s"
        elif minutes > 0:
            return f"{int(minutes)}m {int(seconds)}s"
        else:
            return f"{seconds:.2f}s"

    def generate_combined_report(self, campaign_id, template_id, template_name, folder_name, start_date=None, end_date=None, gcs_manager=None):
        """
        Generate a combined report with WhatsApp delivery and loan data
        Args:
            folder_name: Name of the folder in GCS
            start_date: Start date for filtering WhatsApp entries
            end_date: End date for filtering WhatsApp entries
            gcs_manager: GCS manager for file operations
        """
        # Step 1: Generate WhatsApp delivery report
        whatsapp_summary, whatsapp_detailed = self.generate_report(start_date, end_date)
        
        # Test phone normalization with sample cases
        self._test_phone_normalization()

        # Step 2: Download and process the existing Excel file with loan data
        excel_file_path = f'notices/{campaign_id}/{folder_name}.xlsx'
        try:
            excel_data = gcs_manager.read_file(excel_file_path)
            # Read the existing Excel file with header validation
            # Use dtype=str for phone number columns to prevent scientific notation issues
            excel_bytes = io.BytesIO(excel_data)
            
            # First, read with object dtype to preserve phone numbers
            opportunities_df = pd.read_excel(excel_bytes, header=0, dtype=str)
            
            # Ensure column names are strings and clean them
            opportunities_df.columns = [str(col).strip() if col is not None else f'Column_{i}' 
                                      for i, col in enumerate(opportunities_df.columns)]
            
            # Debug: Print column names and types
            # for i, col in enumerate(opportunities_df.columns):
                # print(f"Column {i}: '{col}' - Type: {type(col)}")
            
            # Debug: Check phone number column content before normalization
            if 'Borrower\'s Number' in opportunities_df.columns:
                sample_phones = opportunities_df['Borrower\'s Number'].head(10).tolist()
                # for i, phone in enumerate(sample_phones):
                    # print(f"Original phone {i}: {phone} (type: {type(phone)})")
                for i, phone in enumerate(sample_phones):
                    normalized = self.normalize_phone_number(phone)
                    # print(f"Normalized phone {i}: {normalized}")
            else:
                print("Warning: 'Borrower\'s Number' column not found!")
                print(f"Available columns: {list(opportunities_df.columns)}")
                
        except Exception as e:
            print(f"Error reading Excel file: {str(e)}")
            raise

        # Generate signed URLs for each loan
        def generate_signed_link(loan_id):
            if pd.isna(loan_id) or not loan_id:  # Handle empty cell values including NaN
                return None
            blob_name = f"{loan_id}.pdf"
            try:
                # Generate a signed URL that expires in 7 days
                file_path = f'notices/{campaign_id}/{template_id}/{template_name}/{blob_name}'
                url = gcs_manager.generate_signed_url(file_path, expiration_seconds=3600*24*7)
                return url
            except Exception as e:
                print(f"Error generating signed URL for {blob_name}: {str(e)}")
                return None

        # Add a new column 'presigned_url' with the generated links
        if 'Loan ID' in opportunities_df.columns:
            opportunities_df['presigned_url'] = opportunities_df['Loan ID'].apply(generate_signed_link)
        else:
            opportunities_df['presigned_url'] = None

        # Create WhatsApp status DataFrame for merging with opportunities
        if whatsapp_detailed:
            whatsapp_df = pd.DataFrame(whatsapp_detailed)
            # Clean and standardize phone numbers for matching
            if 'Phone Number' in whatsapp_df.columns:
                # Apply consistent phone number normalization
                whatsapp_df['Phone Number'] = whatsapp_df['Phone Number'].apply(
                    lambda x: self.normalize_phone_number(x)
                )
                self._debug_phone_numbers(whatsapp_df['Phone Number'], "WhatsApp data for merging")

                # Extract required columns for merging
                whatsapp_status_columns = [
                    'Phone Number', 
                    'Initiated timestamp', 
                    'Sent timestamp', 
                    'Delivered timestamp', 
                    'Read timestamp', 
                    'Failed timestamp', 
                    'Failed Reason'
                ]

                # Select only columns that exist in the DataFrame
                available_columns = [col for col in whatsapp_status_columns if col in whatsapp_df.columns]
                whatsapp_status_df = whatsapp_df[available_columns]
            else:
                whatsapp_status_df = pd.DataFrame(columns=[
                    'Phone Number', 
                    'Initiated timestamp', 
                    'Sent timestamp', 
                    'Delivered timestamp', 
                    'Read timestamp', 
                    'Failed timestamp', 
                    'Failed Reason'
                ])
        else:
            whatsapp_status_df = pd.DataFrame(columns=[
                'Phone Number', 
                'Initiated timestamp', 
                'Sent timestamp', 
                'Delivered timestamp', 
                'Read timestamp', 
                'Failed timestamp', 
                'Failed Reason'
            ])

        # Clean phone numbers in opportunities_df for matching
        if 'Borrower\'s Number' in opportunities_df.columns:
            # Apply consistent phone number normalization
            opportunities_df['Phone_Clean'] = opportunities_df['Borrower\'s Number'].apply(
                lambda x: self.normalize_phone_number(x)
            )
            
            # Remove rows where phone normalization failed
            valid_phone_mask = opportunities_df['Phone_Clean'].notna()
            self._debug_phone_numbers(opportunities_df['Phone_Clean'].dropna(), "Opportunities data for merging")
            
            # Safe counting of unique phone numbers
            whatsapp_unique_count = whatsapp_status_df['Phone Number'].nunique() if not whatsapp_status_df.empty and 'Phone Number' in whatsapp_status_df.columns else 0
            opportunities_unique_count = opportunities_df['Phone_Clean'].nunique()
            # print(f"Unique phone numbers - WhatsApp: {whatsapp_unique_count}, Opportunities: {opportunities_unique_count}")
            
            # Check for overlapping phone numbers
            if 'Phone Number' in whatsapp_status_df.columns and len(whatsapp_status_df) > 0:
                whatsapp_phones = set(whatsapp_status_df['Phone Number'].dropna())
                opportunities_phones = set(opportunities_df['Phone_Clean'].dropna())
                common_phones = whatsapp_phones.intersection(opportunities_phones)
                # print(f"Common phone numbers: {len(common_phones)}")
                # if common_phones:
                #     print(f"Sample common phones: {list(common_phones)[:5]}")
                # else:
                #     print(f"Sample WhatsApp phones: {list(whatsapp_phones)[:5]}")
                #     print(f"Sample Opportunities phones: {list(opportunities_phones)[:5]}")

            # Merge WhatsApp status data into opportunities dataframe
            before_merge_count = len(opportunities_df)
            opportunities_df = pd.merge(
                opportunities_df,
                whatsapp_status_df,
                how='left',
                left_on='Phone_Clean',
                right_on='Phone Number'
            )
            after_merge_count = len(opportunities_df)
            
            # Count successful matches
            matched_count = opportunities_df['Phone Number'].notna().sum()
            # print(f"Merge results: {before_merge_count} -> {after_merge_count} rows")
            # print(f"Successfully matched {matched_count} out of {before_merge_count} records")
            
            # Debug: Show some examples of matched vs unmatched
            matched_examples = opportunities_df[opportunities_df['Phone Number'].notna()][['Loan ID', 'Phone_Clean', 'Initiated timestamp']].head(3)
            unmatched_examples = opportunities_df[opportunities_df['Phone Number'].isna()][['Loan ID', 'Phone_Clean']].head(3)
            # print(f"Matched examples:\n{matched_examples}")
            # print(f"Unmatched examples:\n{unmatched_examples}")
            
            # Specific debug: Check for initiated timestamps
            initiated_count = opportunities_df['Initiated timestamp'].notna().sum()
            # print(f"Records with initiated timestamps: {initiated_count}")
            
            if initiated_count > 0:
                initiated_examples = opportunities_df[opportunities_df['Initiated timestamp'].notna()][['Loan ID', 'Phone_Clean', 'Initiated timestamp']].head(5)
                # print(f"Sample initiated records:\n{initiated_examples}")

            # Drop the temporary clean phone column and duplicate phone number column
            opportunities_df = opportunities_df.drop(['Phone_Clean', 'Phone Number'], axis=1, errors='ignore')

            # Ensure all column names are strings after merge
            opportunities_df.columns = [str(col) if col is not None else f'Column_{i}' 
                                      for i, col in enumerate(opportunities_df.columns)]

            # Format timestamp columns if they exist - using safe string checking
            timestamp_columns = []
            for col in opportunities_df.columns:
                col_str = str(col).lower()
                if 'timestamp' in col_str:
                    timestamp_columns.append(col)
            
            for col in timestamp_columns:
                if col in opportunities_df.columns:
                    opportunities_df[col] = opportunities_df[col].apply(
                        lambda x: x.strftime('%Y-%m-%d %H:%M:%S') if pd.notnull(x) and hasattr(x, 'strftime') else ''
                    )
        else:
            print("Warning: 'Borrower\'s Number' column not found in opportunities DataFrame")

        # Step 3: Create a combined Excel file with multiple sheets
        output = io.BytesIO()

        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            # Sheet 1: Opportunities with presigned URLs and WhatsApp status
            opportunities_df.to_excel(writer, sheet_name='Opportunities', index=False)

            # Create WhatsApp Summary sheet
            summary_data = {
                'Metric': [
                    'Total Messages',
                    'Delivery Rate',
                    'Failed Messages',
                    'Average Delivery Time'
                ],
                'Value': [
                    whatsapp_summary['total_messages'],
                    f"{whatsapp_summary['delivery_rate']}%",
                    whatsapp_summary['failed_messages'],
                    whatsapp_summary['average_delivery_time']
                ]
            }
            df_summary = pd.DataFrame(summary_data)
            df_summary.to_excel(writer, sheet_name='WhatsApp Summary', index=False)

            # Create Status Breakdown sheet
            status_data = [{
                'Status': status['status'],
                'Description': self.status_map.get(status['status'], 'Unknown status'),
                'Count': status['count']
            } for status in whatsapp_summary['status_breakdown']]
            df_status = pd.DataFrame(status_data) if status_data else pd.DataFrame(columns=['Status', 'Description', 'Count'])
            df_status.to_excel(writer, sheet_name='Status Breakdown', index=False)

            # Create Message History sheet for detailed WhatsApp data
            detailed_columns = [
                'S.No', 'Phone Number', 'Initiated timestamp', 'Sent timestamp', 
                'Delivered timestamp', 'Read timestamp', 'Failed timestamp', 'Failed Reason'
            ]

            # Create detailed DataFrame
            if not whatsapp_detailed:
                df_detailed = pd.DataFrame(columns=detailed_columns)
            else:
                df_detailed = pd.DataFrame(whatsapp_detailed)
                # Ensure all columns exist (even if empty)
                for col in detailed_columns:
                    if col not in df_detailed.columns:
                        df_detailed[col] = None

                # Reorder columns to match requested format
                df_detailed = df_detailed[detailed_columns]

                # Format timestamp columns
                timestamp_columns = [col for col in df_detailed.columns if 'timestamp' in str(col).lower()]
                for col in timestamp_columns:
                    df_detailed[col] = df_detailed[col].apply(
                        lambda x: x.strftime('%Y-%m-%d %H:%M:%S') if pd.notnull(x) and hasattr(x, 'strftime') else ''
                    )

            df_detailed.to_excel(writer, sheet_name='Message History', index=False)

            # Format all sheets with proper styling
            self._format_excel_workbook(writer, df_summary, df_status, df_detailed, opportunities_df)

        output.seek(0)
        return output

    def _format_excel_workbook(self, writer, df_summary, df_status, df_detailed, opportunities_df=None):
        """Apply consistent formatting to Excel workbook"""
        workbook = writer.book

        # Format for headers
        header_format = workbook.add_format({'bold': True, 'bg_color': '#D9E1F2', 'border': 1})

        # Apply header format to all sheets
        for sheet_name in writer.sheets:
            worksheet = writer.sheets[sheet_name]

            # Get column names based on the sheet
            if sheet_name == 'Opportunities' and opportunities_df is not None:
                columns = opportunities_df.columns
            elif sheet_name == 'WhatsApp Summary':
                columns = df_summary.columns
            elif sheet_name == 'Status Breakdown':
                columns = df_status.columns
            elif sheet_name == 'Message History':
                columns = df_detailed.columns
            else:
                continue  # Skip if sheet name is not recognized

            # Write headers with format
            for col_num, column_name in enumerate(columns):
                worksheet.write(0, col_num, str(column_name), header_format)

            # Add autofilter and freeze panes
            worksheet.autofilter(0, 0, 0, len(columns) - 1)
            worksheet.freeze_panes(1, 0)  # Freeze the header row

            # Set column widths for each sheet
            if sheet_name == 'Message History':
                for i, col in enumerate(df_detailed.columns):
                    max_width = max(
                        len(str(col)) + 2,
                        df_detailed[col].astype(str).str.len().max() + 2 if len(df_detailed) > 0 else 15
                    )
                    worksheet.set_column(i, i, min(max_width, 30))
            elif sheet_name == 'Opportunities' and opportunities_df is not None:
                for i, col in enumerate(opportunities_df.columns):
                    col_str = str(col).lower()
                    # Special handling for the URL column and timestamp columns
                    if col_str == 'presigned_url':
                        worksheet.set_column(i, i, 50)
                    elif 'timestamp' in col_str or col_str == 'failed reason':
                        worksheet.set_column(i, i, 25)
                    else:
                        max_width = max(
                            len(str(col)) + 2,
                            opportunities_df[col].astype(str).str.len().max() + 2 if len(opportunities_df) > 0 else 15
                        )
                        worksheet.set_column(i, i, min(max_width, 30))
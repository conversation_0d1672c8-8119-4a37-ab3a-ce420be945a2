from sqlalchemy import text, bindparam, create_engine
from sqlalchemy.types import DateTime
from sqlalchemy.orm import sessionmaker
import os

# Configure environment variables
INSTANCE_CONN_NAME = os.environ['INSTANCE_CONNECTION_NAME']
DB_CONFIG = {
    'user': os.environ['DB_USER'],
    'password': os.environ['DB_PASSWORD'],
    'database': os.environ['DB_NAME'],
    'host': f'/cloudsql/{INSTANCE_CONN_NAME}'
}

def get_whatsapp_entries(pool, start_date=None, end_date=None):
    """Retrieve entries using SQLAlchemy Core with proper parameter typing"""
    query = text("""
        SELECT * FROM notice_entry 
        WHERE (:start_date IS NULL OR timestamp >= :start_date)
        AND (:end_date IS NULL OR timestamp <= :end_date)
    """).bindparams(
        bindparam('start_date', value=start_date, type_=DateTime),
        bindparam('end_date', value=end_date, type_=DateTime)
    )

    try:
        with pool.connect() as connection:
            result = connection.execute(query)
            return [dict(row) for row in result.mappings()]
    except Exception as e:
        print(f"Database query failed: {str(e)}")
        raise

def close_db_connections(pool):
    """Dispose connection pool when shutting down"""
    pool.dispose()
    print("Database connection pool closed")

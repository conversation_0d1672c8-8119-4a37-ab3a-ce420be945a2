import os
from google.cloud import storage
from datetime import datetime, timedelta
from google.oauth2 import service_account
import json
import sendgrid

class GoogleCloudStorageManager:
    def __init__(self, credentials: service_account.Credentials, bucket_name: str):
        self._storage_client = storage.Client(credentials=credentials)
        self._bucket_name = bucket_name
    
    def read_file(self, file_path):
        """Read a file from GCS"""
        bucket = self._storage_client.bucket(self._bucket_name)
        blob = bucket.blob(file_path)
        return blob.download_as_bytes()
    
    def generate_signed_url(self, file_path, expiration_seconds=604800):
        """Generate a signed URL for the file that expires after the specified time"""
        bucket = self._storage_client.bucket(self._bucket_name)
        blob = bucket.blob(file_path)
        url = blob.generate_signed_url(
            version="v4",
            expiration=datetime.utcnow() + timedelta(seconds=expiration_seconds),
            method="GET"
        )
        return url
    
    def upload_file(self, file_path, file_content):
        """Upload a file to GCS"""
        bucket = self._storage_client.bucket(self._bucket_name)
        blob = bucket.blob(file_path)
        blob.upload_from_file(file_content)
        return blob.public_url

# def get_gcs_manager():
#     """Get a GCS manager instance"""
#     bucket_name = os.environ.get('GCS_BUCKET_NAME')
#     return GCSManager(bucket_name)

credentials_json = os.environ.get('GCP_CREDENTIALS').strip()
credentials_dict = json.loads(credentials_json)
STORAGE_CREDENTIALS = service_account.Credentials.from_service_account_info(credentials_dict)

gcs_manager = GoogleCloudStorageManager(
    credentials=STORAGE_CREDENTIALS,
    bucket_name=os.environ.get('GCS_BUCKET_NAME')
)

def upload_to_gcs(gcs_manager, file_path, file_content):
    """Upload a file to GCS and return the public URL"""
    return gcs_manager.upload_file(file_path, file_content)


SENDGRID_API_KEY=os.environ.get('SENDGRID_API_KEY')
sg = sendgrid.SendGridAPIClient(api_key=SENDGRID_API_KEY)
MAIL_ID = "<EMAIL>"
def sendMail(to, subject, value=""):
    mail = sendgrid.helpers.mail.Mail(from_email=MAIL_ID, to_emails=to, subject=subject, html_content=value)
    response = sg.send(mail)
    # print("response status code from sendgrid for email sending: ", response.status_code)
    print("response body from sendgrid: ", response.body)
    # print(response.headers)
